# A.A. Chips Blog Migration Checklist
## From XAMPP Localhost to Production Webhost

### ✅ COMPLETED LOCALLY
- [x] Database export script created (`export-database.php`)
- [x] Database successfully exported (`aachipsc_production_export.sql`)
- [x] Secure configuration template created (`secure_config_template.php`)
- [x] All 10 database tables exported with data

---

## 🚀 MIGRATION STEPS

### Step 1: Prepare Your Webhost
- [ ] Log into your webhost control panel (cPanel/Plesk)
- [ ] Create new MySQL database
  - [ ] Note database name: `aachipscomments`
  - [ ] Note database username: `_________________`
  - [ ] Note database password: `_________________`
- [ ] Access PHPMyAdmin on your webhost

### Step 2: Upload Database
- [ ] Open PHPMyAdmin on your webhost
- [ ] Select your new database
- [ ] Go to "Import" tab
- [ ] Upload `aachipsc_production_export.sql`
- [ ] Click "Go" to import
- [ ] Verify all 10 tables were created:
  - [ ] aachipsc_blog_comment_votes
  - [ ] aachipsc_blog_comments
  - [ ] aachipsc_blog_daily_stats
  - [ ] aachipsc_blog_page_stats
  - [ ] aachipsc_blog_page_visits
  - [ ] aachipsc_blog_rate_limits
  - [ ] aachipsc_blog_site_stats
  - [ ] aachipsc_blog_spam_patterns
  - [ ] aachipsc_blog_users
  - [ ] aachipsc_blog_visitor_sessions

### Step 3: Create Secure Configuration
- [ ] Navigate to your account root (one level above `public_html`)
- [ ] Create directory: `secure_config`
- [ ] Set directory permissions to `755`
- [ ] Copy `secure_config_template.php` to `../secure_config/obq_comments.php`
- [ ] Edit the config file and update:
  - [ ] Database host (usually `localhost`)
  - [ ] Database name (your actual database name)
  - [ ] Database username (your actual username)
  - [ ] Database password (your actual password)
  - [ ] Notification email (your email address)
  - [ ] Timezone (your timezone)
- [ ] Set file permissions to `600` (read/write for owner only)

### Step 4: Upload Website Files
Upload to `public_html` (or your domain folder):

#### Core Files to Upload:
- [ ] `index.php`
- [ ] `config.php`
- [ ] `path-helper.php`
- [ ] `gallery.php`
- [ ] `favicon.ico`

#### Directories to Upload:
- [ ] `content/` (entire directory)
- [ ] `css/` (entire directory)
- [ ] `js/` (entire directory)
- [ ] `img/` (entire directory)
- [ ] `includes/` (entire directory)
- [ ] `comments/` (but exclude `local_config.php`)
- [ ] `visitor-counter/` (entire directory)
- [ ] `api/` (entire directory)
- [ ] `data/` (entire directory)

#### Files NOT to Upload:
- [ ] Skip: `comments/local_config.php`
- [ ] Skip: `setup-database.php`
- [ ] Skip: `test-*.php` files
- [ ] Skip: `export-database.php`
- [ ] Skip: `secure_config_template.php`
- [ ] Skip: `MIGRATION_CHECKLIST.md`
- [ ] Skip: `*.md` documentation files

### Step 5: Set File Permissions
- [ ] PHP files: `644`
- [ ] Directories: `755`
- [ ] Secure config file: `600`

### Step 6: Test Your Production Site
- [ ] Visit your domain
- [ ] Check homepage loads correctly
- [ ] Navigate to a blog post
- [ ] Test comment submission
- [ ] Verify comments display
- [ ] Check visitor counter functionality
- [ ] Test gallery if applicable
- [ ] Verify all images load correctly

### Step 7: Security & Cleanup
- [ ] Remove development files from production:
  - [ ] Delete any remaining `test-*.php` files
  - [ ] Delete `secure_config_template.php`
  - [ ] Delete `MIGRATION_CHECKLIST.md`
- [ ] Check error logs in cPanel
- [ ] Verify HTTPS is working (SSL certificate)
- [ ] Test from different devices/browsers

---

## 🔧 TROUBLESHOOTING

### Database Connection Issues:
- [ ] Verify database credentials in secure config
- [ ] Check if database user has proper permissions
- [ ] Ensure database exists and is accessible
- [ ] Check error logs in cPanel

### Comments Not Working:
- [ ] Verify secure config file exists at `../secure_config/obq_comments.php`
- [ ] Check file permissions on config file
- [ ] Test database connection manually
- [ ] Check browser console for JavaScript errors

### File Permission Errors:
- [ ] Set directories to `755`
- [ ] Set PHP files to `644`
- [ ] Ensure web server can read files

### Images Not Loading:
- [ ] Check image file paths
- [ ] Verify images were uploaded correctly
- [ ] Check file permissions on img directory

---

## 📋 POST-MIGRATION TASKS

### Immediate:
- [ ] Test all major functionality
- [ ] Post a test comment and verify it works
- [ ] Check visitor counter is incrementing
- [ ] Verify email notifications work (if enabled)

### Within 24 Hours:
- [ ] Monitor error logs
- [ ] Test from multiple devices
- [ ] Check site performance
- [ ] Verify search engine accessibility

### Ongoing:
- [ ] Set up regular database backups
- [ ] Monitor site performance
- [ ] Keep local development environment in sync
- [ ] Plan for future updates

---

## 📞 SUPPORT RESOURCES

### If You Need Help:
1. **Webhost Support**: Contact your hosting provider for database/server issues
2. **Error Logs**: Check cPanel error logs for specific error messages
3. **Database Issues**: Use PHPMyAdmin to verify database structure
4. **File Issues**: Use File Manager to check file permissions

### Common Webhost Database Patterns:
- **Shared Hosting**: Database name usually includes your username
- **cPanel**: Database management in "MySQL Databases" section
- **Plesk**: Database management in "Databases" section

---

**Remember**: Keep your local XAMPP environment as a backup and development space!