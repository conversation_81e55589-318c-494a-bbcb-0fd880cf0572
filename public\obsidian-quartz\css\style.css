/* Base styles */
:root {
  /* Default Theme (Light) */
  --primary-color: #6b5b95;
  --secondary-color: #88d8b0;
  --accent-color: #ff6b6b;
  --text-color: #2c3e50;
  --text-muted: #5a6c7d;
  --text-light: #7f8c8d;
  --background-color: #ffffff;
  --card-background: #f8f9fa;
  --header-background: #6b5b95;
  --footer-background: #34495e;
  --link-color: #6b5b95;
  --link-hover-color: #88d8b0;
  --tag-background: #e9ecef;
  --border-color: #dee2e6;
  --border-color-light: #e9ecef;
  --hover-background: #f1f3f4;
  --active-background: #e9ecef;
  --code-background: #f1f3f4;
  --quote-background: #f8f9fa;
  --quote-text: #5a6c7d;
  --success-background: #d4edda;
  --success-text: #155724;
  --success-border: #c3e6cb;
  --error-background: #f8d7da;
  --error-text: #721c24;
  --error-border: #f5c6cb;
  --modal-overlay: rgba(0,0,0,0.5);
  --modal-background: rgba(255,255,255,0.95);
  --shadow-light: rgba(0,0,0,0.1);
  --shadow-medium: rgba(0,0,0,0.15);
  --primary-alpha-15: rgba(107, 91, 149, 0.15);
  --primary-alpha-20: rgba(107, 91, 149, 0.2);
  --primary-alpha-30: rgba(107, 91, 149, 0.3);
  --secondary-alpha-25: rgba(136, 216, 176, 0.25);
  --white-alpha-10: rgba(255, 255, 255, 0.1);
  --white-alpha-30: rgba(255,255,255,0.3);
  --white-alpha-90: rgba(255,255,255,0.9);

  /* Layout variables */
  --border-radius: 8px;
  --box-shadow: 0 2px 5px var(--shadow-light);
  --transition: all 0.3s ease;
}

/* Light Theme */
[data-theme="light"] {
  --primary-color: #6b5b95;
  --secondary-color: #88d8b0;
  --accent-color: #ff6b6b;
  --text-color: #2c3e50;
  --text-muted: #5a6c7d;
  --text-light: #7f8c8d;
  --background-color: #ffffff;
  --card-background: #f8f9fa;
  --header-background: #6b5b95;
  --footer-background: #34495e;
  --link-color: #6b5b95;
  --link-hover-color: #88d8b0;
  --tag-background: #e9ecef;
  --border-color: #dee2e6;
  --border-color-light: #e9ecef;
  --hover-background: #f1f3f4;
  --active-background: #e9ecef;
  --code-background: #f1f3f4;
  --quote-background: #f8f9fa;
  --quote-text: #5a6c7d;
  --success-background: #d4edda;
  --success-text: #155724;
  --success-border: #c3e6cb;
  --error-background: #f8d7da;
  --error-text: #721c24;
  --error-border: #f5c6cb;
  --modal-overlay: rgba(0,0,0,0.5);
  --modal-background: rgba(255,255,255,0.95);
  --shadow-light: rgba(0,0,0,0.1);
  --shadow-medium: rgba(0,0,0,0.15);
  --primary-alpha-15: rgba(107, 91, 149, 0.15);
  --primary-alpha-20: rgba(107, 91, 149, 0.2);
  --primary-alpha-30: rgba(107, 91, 149, 0.3);
  --secondary-alpha-25: rgba(136, 216, 176, 0.25);
  --white-alpha-10: rgba(255, 255, 255, 0.1);
  --white-alpha-30: rgba(255,255,255,0.3);
  --white-alpha-90: rgba(255,255,255,0.9);
}

/* Dark Theme */
[data-theme="dark"] {
  --primary-color: #a991e5;
  --secondary-color: #c2e59c;
  --accent-color: #ff9a8b;
  --text-color: #f0f0f0;
  --text-muted: #bbbbbb;
  --text-light: #999;
  --background-color: #222;
  --card-background: #333;
  --header-background: #444;
  --footer-background: #111;
  --link-color: #a991e5;
  --link-hover-color: #c2e59c;
  --tag-background: #444;
  --border-color: #444;
  --border-color-light: #555;
  --hover-background: #444;
  --active-background: #555;
  --code-background: #444;
  --quote-background: #2a2a2a;
  --quote-text: #ccc;
  --success-background: #1e4d2b;
  --success-text: #4ade80;
  --success-border: #166534;
  --error-background: #4c1d24;
  --error-text: #f87171;
  --error-border: #7f1d1d;
  --modal-overlay: rgba(0,0,0,0.95);
  --modal-background: rgba(0,0,0,0.9);
  --shadow-light: rgba(0,0,0,0.3);
  --shadow-medium: rgba(0,0,0,0.4);
  --primary-alpha-15: rgba(169, 145, 229, 0.15);
  --primary-alpha-20: rgba(169, 145, 229, 0.2);
  --primary-alpha-30: rgba(169, 145, 229, 0.3);
  --secondary-alpha-25: rgba(194, 229, 156, 0.25);
  --white-alpha-10: rgba(255, 255, 255, 0.05);
  --white-alpha-30: rgba(255,255,255,0.1);
  --white-alpha-90: rgba(255,255,255,0.8);
}

/* Gradient Animation Theme */
[data-theme="gradient"] {
  --primary-color: #8b5cf6;
  --secondary-color: #06d6a0;
  --accent-color: #f72585;
  --text-color: #1a1a1a;
  --text-muted: #4a4a4a;
  --text-light: #666;
  --background-color: #fefefe;
  --card-background: rgba(255, 255, 255, 0.9);
  --header-background: linear-gradient(135deg, #8b5cf6, #06d6a0);
  --footer-background: #2a2a2a;
  --link-color: #8b5cf6;
  --link-hover-color: #06d6a0;
  --tag-background: rgba(139, 92, 246, 0.1);
  --border-color: rgba(139, 92, 246, 0.2);
  --border-color-light: rgba(139, 92, 246, 0.1);
  --hover-background: rgba(139, 92, 246, 0.05);
  --active-background: rgba(139, 92, 246, 0.1);
  --code-background: rgba(139, 92, 246, 0.05);
  --quote-background: rgba(6, 214, 160, 0.05);
  --quote-text: #2a2a2a;
  --success-background: rgba(6, 214, 160, 0.1);
  --success-text: #065f46;
  --success-border: rgba(6, 214, 160, 0.3);
  --error-background: rgba(247, 37, 133, 0.1);
  --error-text: #7c2d12;
  --error-border: rgba(247, 37, 133, 0.3);
  --modal-overlay: rgba(139, 92, 246, 0.2);
  --modal-background: rgba(255, 255, 255, 0.95);
  --shadow-light: rgba(139, 92, 246, 0.1);
  --shadow-medium: rgba(139, 92, 246, 0.2);
  --primary-alpha-15: rgba(139, 92, 246, 0.15);
  --primary-alpha-20: rgba(139, 92, 246, 0.2);
  --primary-alpha-30: rgba(139, 92, 246, 0.3);
  --secondary-alpha-25: rgba(6, 214, 160, 0.25);
  --white-alpha-10: rgba(255, 255, 255, 0.1);
  --white-alpha-30: rgba(255,255,255,0.3);
  --white-alpha-90: rgba(255,255,255,0.9);
}

/* Gradient animation for gradient theme */
[data-theme="gradient"] body {
  background: linear-gradient(-45deg, #e0e7ff, #f0f9ff, #ecfdf5, #fef3c7, #fce7f3, #e0e7ff);
  background-size: 600% 600%;
  animation: gradientShift 20s ease infinite;
  min-height: 100vh;
}

[data-theme="gradient"] .main-container {
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.1),
    rgba(6, 214, 160, 0.1),
    rgba(247, 37, 133, 0.1),
    rgba(59, 130, 246, 0.1),
    rgba(139, 92, 246, 0.1)
  );
  background-size: 400% 400%;
  animation: containerGradientShift 25s ease infinite;
  border-radius: var(--border-radius);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.15);
}

[data-theme="gradient"] header {
  background: linear-gradient(135deg, #8b5cf6, #06d6a0, #f72585, #3b82f6);
  background-size: 300% 300%;
  animation: headerGradientShift 12s ease infinite;
}

[data-theme="gradient"] .card {
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,250,252,0.9));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="gradient"] .card:hover {
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(241,245,249,0.95));
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
  transform: translateY(-5px);
}

[data-theme="gradient"] .post-card {
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(248,250,252,0.9));
  backdrop-filter: blur(10px);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="gradient"] .post-card:hover {
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(241,245,249,0.95));
  box-shadow: 0 8px 32px rgba(139, 92, 246, 0.3);
}

[data-theme="gradient"] .sidebar {
  background: linear-gradient(135deg, rgba(255,255,255,0.8), rgba(248,250,252,0.8));
  backdrop-filter: blur(15px);
  border: 1px solid rgba(139, 92, 246, 0.15);
}

[data-theme="gradient"] .category-card {
  background: linear-gradient(135deg, rgba(255,255,255,0.7), rgba(248,250,252,0.7));
  backdrop-filter: blur(8px);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="gradient"] .category-card:hover {
  background: linear-gradient(135deg, rgba(255,255,255,0.9), rgba(241,245,249,0.9));
  box-shadow: 0 6px 24px rgba(139, 92, 246, 0.25);
}

[data-theme="gradient"] a.internal-link {
  background: linear-gradient(to bottom, transparent 70%, rgba(139, 92, 246, 0.15) 70%);
}

[data-theme="gradient"] a.internal-link:hover {
  background: linear-gradient(to bottom, transparent 60%, rgba(6, 214, 160, 0.25) 60%);
}

/* Additional gradient theme enhancements for page background */
[data-theme="gradient"] .container {
  position: relative;
}

/* Subtle animated overlay for extra depth */
[data-theme="gradient"] body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg,
    rgba(139, 92, 246, 0.02),
    rgba(6, 214, 160, 0.02),
    rgba(247, 37, 133, 0.02),
    rgba(59, 130, 246, 0.02)
  );
  background-size: 800% 800%;
  animation: overlayGradientShift 35s ease infinite;
  pointer-events: none;
  z-index: -1;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 50%; }
}

@keyframes containerGradientShift {
  0% { background-position: 0% 0%; }
  25% { background-position: 100% 0%; }
  50% { background-position: 100% 100%; }
  75% { background-position: 0% 100%; }
  100% { background-position: 0% 0%; }
}

@keyframes headerGradientShift {
  0% { background-position: 0% 50%; }
  33% { background-position: 100% 50%; }
  66% { background-position: 50% 100%; }
  100% { background-position: 0% 50%; }
}

@keyframes overlayGradientShift {
  0% { background-position: 0% 0%; }
  20% { background-position: 100% 0%; }
  40% { background-position: 100% 100%; }
  60% { background-position: 0% 100%; }
  80% { background-position: 0% 50%; }
  100% { background-position: 0% 0%; }
}

body {
  font-family: 'Open Sans', sans-serif;
  line-height: 1.6;
  color: var(--text-color);
  background-color: var(--background-color);
  margin: 0;
  padding: 0;
}

* {
  box-sizing: border-box;
}

img {
  width: 100%;
  height: auto;
  max-width: 100%;
  display: block;
  margin: 1rem auto;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Montserrat', sans-serif;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
  color: var(--primary-color);
}

h1 {
  font-size: 2.5rem;
  border-bottom: 2px solid var(--secondary-color);
  padding-bottom: 0.3em;
}

/* Ensure h1 in content areas uses text color for better readability */
.content h1,
.content-header h1,
.main-container h1 {
  color: var(--text-color);
}

h2 {
  font-size: 2rem;
}

h3 {
  font-size: 1.5rem;
}

/* Base link styles - subtle and text-integrated */
a {
  color: var(--link-color);
  text-decoration: none;
  transition: var(--transition);
  border-bottom: 1px solid transparent;
}

a:hover {
  color: var(--link-hover-color);
  border-bottom: 1px solid var(--link-hover-color);
}

/* Internal links - blend with text but remain visible */
a.internal-link {
  color: var(--link-color);
  background: linear-gradient(to bottom, transparent 70%, var(--primary-alpha-15) 70%);
  border-bottom: 1px dotted var(--link-color);
  padding: 0 2px;
  border-radius: 2px;
  font-weight: 500;
}

a.internal-link:hover {
  background: linear-gradient(to bottom, transparent 60%, var(--secondary-alpha-25) 60%);
  border-bottom: 1px solid var(--link-hover-color);
  color: var(--link-hover-color);
}

/* External links - slightly more prominent */
a.external-link {
  color: var(--link-color);
  border-bottom: 1px solid var(--primary-alpha-30);
}

a.external-link:hover {
  color: var(--link-hover-color);
  border-bottom: 1px solid var(--link-hover-color);
}

a.external-link::after {
  content: " ↗";
  font-size: 0.8em;
  opacity: 0.6;
}

p {
  margin-bottom: 1.5em;
}

blockquote {
  border-left: 4px solid var(--secondary-color);
  padding-left: 1em;
  margin-left: 0;
  font-style: italic;
  color: var(--text-muted);
}

.content {
  background-color: var(--card-background);
  color: var(--text-color);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
  text-align: left;
}

/* Content h1 styling moved to typography section above */

iframe,
img {
  margin: 0 auto;
  display: block;
}

/* Button-style links only for specific cases */
.content a.btn,
.content a.button,
.btn,
.button {
  display: inline-block;
  background-color: var(--primary-color);
  color: var(--link-color);
  padding: 0.5rem 1.5rem;
  border-radius: var(--border-radius);
  text-decoration: none;
  transition: var(--transition);
  border-bottom: none;
}

.content a.btn:hover,
.content a.button:hover,
.btn:hover,
.button:hover {
  background-color: var(--link-hover-color);
  border-bottom: none;
}

code {
  background-color: var(--code-background);
  padding: 0.2em 0.4em;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

/* Layout */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
}

.content-wrapper {
  display: flex;
  flex-wrap: wrap;
  margin-top: 2rem;
}

/* Main Layout Container */
.main-container {
  display: flex;
  max-width: 1400px;
  margin: 2rem auto;
  padding: 1rem;
  gap: 2rem;
  align-items: flex-start;
}

main {
  flex: 1;
  min-width: 0; /* Prevents flex item from overflowing */
}

.sidebar {
  width: 350px;
  flex-shrink: 0;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  position: sticky;
  top: 2rem;
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
}

/* Browse Categories Section */
.browse-categories {
  margin-top: 3rem;
  padding-top: 2rem;
  border-top: 2px solid var(--secondary-color);
}

.browse-categories h2 {
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 2rem;
  font-size: 1.8rem;
}

.categories-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.category-card {
  display: block;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  text-decoration: none;
  color: var(--text-color);
  border: 1px solid #eee;
  box-shadow: var(--box-shadow);
  transition: all 0.3s ease;
  cursor: pointer;
}

.category-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px var(--shadow-medium);
  text-decoration: none;
  color: var(--text-color);
}

.category-card h3 {
  margin: 0 0 0.8rem 0;
  color: var(--primary-color);
  font-size: 1.3rem;
  transition: color 0.3s ease;
}

.category-card:hover h3 {
  color: var(--secondary-color);
}

.category-card p {
  margin: 0;
  color: var(--text-muted);
  line-height: 1.5;
  font-size: 0.95rem;
}

/* Header */
header {
  background-color: var(--header-background);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.site-branding {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.site-logo {
  width: 100px;
  height: 100px;
  object-fit: contain;
  aspect-ratio: 1 / 1;
  border-radius: 8px;
  background-color: rgba(255, 255, 255, 0.1);
  padding: 0.5rem;
}

.site-title {
  font-size: 1.8rem;
  margin: 0;
  color: white;
  text-decoration: none;
  font-family: 'Montserrat', sans-serif;
  font-weight: 600;
}

.site-title:hover {
  color: var(--secondary-color);
  text-decoration: none;
}

/* Navigation */
nav ul {
  list-style: none;
  display: flex;
  margin: 0;
  padding: 0;
}

nav li {
  margin-left: 1.5rem;
}

nav a {
  color: white;
  text-decoration: none;
  font-weight: 600;
  transition: var(--transition);
}

nav a:hover {
  color: var(--secondary-color);
  text-decoration: none;
}

/* Color Switcher Component */
.color-switcher {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 1rem;
}

.color-switcher-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 20px;
  padding: 0.25rem;
  cursor: pointer;
  transition: var(--transition);
  min-width: 120px;
  justify-content: space-between;
}

.color-switcher-toggle:hover {
  background: rgba(255, 255, 255, 0.15);
  border-color: rgba(255, 255, 255, 0.3);
}

.theme-option {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: var(--transition);
  cursor: pointer;
  position: relative;
  z-index: 2;
}

.theme-option.active {
  background: white;
  color: var(--primary-color);
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.theme-option:not(.active) {
  color: rgba(255, 255, 255, 0.7);
}

.theme-option:hover:not(.active) {
  color: white;
  background: rgba(255, 255, 255, 0.1);
}

.theme-icon {
  font-size: 14px;
  transition: var(--transition);
}

/* Theme-specific icons */
.theme-light .theme-icon::before { content: "☀️"; }
.theme-dark .theme-icon::before { content: "🌙"; }
.theme-gradient .theme-icon::before { content: "🌈"; }

/* Light theme adjustments for color switcher */
[data-theme="light"] .color-switcher-toggle {
  background: rgba(107, 91, 149, 0.1);
  border: 1px solid rgba(107, 91, 149, 0.2);
}

[data-theme="light"] .color-switcher-toggle:hover {
  background: rgba(107, 91, 149, 0.15);
  border-color: rgba(107, 91, 149, 0.3);
}

[data-theme="light"] .theme-option:not(.active) {
  color: rgba(107, 91, 149, 0.7);
}

[data-theme="light"] .theme-option:hover:not(.active) {
  color: var(--primary-color);
  background: rgba(107, 91, 149, 0.1);
}

[data-theme="light"] .theme-option.active {
  background: var(--primary-color);
  color: white;
  box-shadow: 0 2px 4px rgba(107, 91, 149, 0.3);
}

.mobile-menu-toggle {
  display: none;
  background: none;
  border: none;
  color: white;
  font-size: 1.5rem;
  cursor: pointer;
}

/* Responsive Header */
@media (max-width: 768px) {
  .header-container {
    flex-direction: column;
    gap: 1rem;
  }

  .site-branding {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }

  .site-logo {
    width: 80px;
    height: 80px;
  }

  .site-title {
    font-size: 1.5rem;
  }

  nav ul {
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.5rem;
  }

  nav li {
    margin-left: 0;
    margin: 0.25rem;
  }

  .color-switcher {
    margin-left: 0;
    margin-top: 0.5rem;
    justify-content: center;
  }

  .color-switcher-toggle {
    min-width: 100px;
  }
}

/* Cards */
.card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: 2rem;
  overflow: hidden;
  transition: var(--transition);
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.card-content {
  padding: 1.5rem;
}

.card-title {
  margin-top: 0;
  margin-bottom: 0.5rem;
}

.card-excerpt {
  margin-bottom: 1rem;
  color: #666;
}

.card-meta {
  font-size: 0.9rem;
  color: #888;
  margin-bottom: 0.5rem;
}

.card-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 1rem;
}

.tag {
  background-color: var(--tag-background);
  border-radius: 20px;
  padding: 0.3rem 0.8rem;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  font-size: 0.8rem;
  transition: var(--transition);
}

.tag:hover {
  background-color: var(--secondary-color);
  color: white;
}

/* Gallery */
.gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  grid-gap: 1rem;
  margin: 2rem 0;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
}

.gallery-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
  transition: var(--transition);
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-caption {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: rgba(0,0,0,0.7);
  color: white;
  padding: 0.5rem;
  transform: translateY(100%);
  transition: var(--transition);
}

.gallery-item:hover .gallery-caption {
  transform: translateY(0);
}

/* Music Player */
.music-player {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: 1.5rem;
  margin-bottom: 2rem;
}

.playlist {
  list-style: none;
  padding: 0;
  margin: 1rem 0 0 0;
}

.playlist-item {
  display: flex;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid #eee;
  cursor: pointer;
  transition: var(--transition);
}

.playlist-item:hover {
  background-color: #f5f5f5;
}

.playlist-item.active {
  background-color: #f0f0f0;
  font-weight: bold;
}

.playlist-item-number {
  width: 30px;
  text-align: center;
  color: #888;
}

.playlist-item-title {
  flex: 1;
}

.playlist-item-duration {
  color: #888;
  font-size: 0.9rem;
}

.player-controls {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 1rem;
}

.player-progress {
  height: 5px;
  background-color: #eee;
  border-radius: 5px;
  margin-top: 1rem;
  overflow: hidden;
}

.player-progress-bar {
  height: 100%;
  background-color: var(--secondary-color);
  width: 0%;
}

/* Footer */
footer {
  background-color: var(--footer-background);
  color: white;
  padding: 2rem 0;
  margin-top: 3rem;
}

.footer-content {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.footer-section {
  flex: 1;
  min-width: 250px;
  margin-bottom: 1.5rem;
}

.footer-section h3 {
  color: white;
  border-bottom: 2px solid var(--secondary-color);
  padding-bottom: 0.5rem;
  margin-bottom: 1rem;
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: 0.5rem;
}

.footer-links a {
  color: #ccc;
  text-decoration: none;
  transition: var(--transition);
}

.footer-links a:hover {
  color: white;
}

.footer-bottom {
  text-align: center;
  padding-top: 1.5rem;
  margin-top: 1.5rem;
  border-top: 1px solid #555;
  font-size: 0.9rem;
  color: #aaa;
}

.skip-link {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.skip-link:focus {
  position: fixed;
  top: 10px;
  left: 10px;
  width: auto;
  height: auto;
  padding: 10px;
  margin: 0;
  overflow: visible;
  clip: auto;
  white-space: normal;
  border: 2px solid #000;
  background-color: #fff;
  color: #000;
  font-size: 1.25rem;
  z-index: 1000;
}

          /* #related, #about, #donate, #categories, #connect */
#related {
  margin: 1rem;
  padding: 1rem;
}


/* Category Index Styles */
.category-index {
  max-width: 100%;
}

.category-header {
  text-align: center;
  margin-bottom: 2rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--secondary-color);
}

.category-header h1 {
  color: var(--primary-color);
  margin-bottom: 0.5rem;
}

.category-description {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.post-card {
  background-color: var(--card-background);
  border-radius: var(--border-radius);
  padding: 1.5rem;
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  border: 1px solid #eee;
}

.post-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.post-card .post-title {
  margin-top: 0;
  margin-bottom: 1rem;
  font-size: 1.3rem;
}

.post-card .post-title a {
  color: var(--text-color);
  text-decoration: none;
}

.post-card .post-excerpt {
  margin-bottom: 1rem;
  color: #666;
  line-height: 1.5;
}

.post-card .post-meta {
  font-size: 0.9rem;
  color: #888;
  margin-bottom: 1rem;
}

.post-card .post-meta .post-author {
  font-weight: 600;
}

.post-card .post-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 1rem;
}

.post-card .post-tags .tag {
  background-color: var(--tag-background);
  color: var(--text-color);
  padding: 0.2rem 0.6rem;
  border-radius: 15px;
  font-size: 0.8rem;
  border: 1px solid #ddd;
}

/* Sidebar Enhancements */
.sidebar section {
  margin-bottom: 2rem;
  padding-bottom: 1.5rem;
  border-bottom: 1px solid #eee;
}

.sidebar section:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.sidebar h3 {
  color: var(--primary-color);
  margin-bottom: 1rem;
  font-size: 1.2rem;
  border-bottom: 2px solid var(--secondary-color);
  padding-bottom: 0.5rem;
}

.sidebar ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.sidebar li {
  margin-bottom: 0.8rem;
  padding-left: 1rem;
  position: relative;
}

.sidebar li:before {
  content: "→";
  position: absolute;
  left: 0;
  color: var(--secondary-color);
  font-weight: bold;
}

.sidebar .internal-link {
  color: var(--text-color);
  text-decoration: none;
  transition: color 0.3s ease;
  font-size: 0.95rem;
  line-height: 1.4;
}

.sidebar .internal-link:hover {
  color: var(--primary-color);
  text-decoration: underline;
}

/* Random Posts specific styling */
#random-posts ul {
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Related Posts Grid in Sidebar */
.related-posts-grid {
  display: grid;
  gap: 1rem;
}

.related-post-item {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e9ecef;
  transition: all 0.3s ease;
}

.related-post-item:hover {
  background-color: #e9ecef;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.related-post-item h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.related-post-item .related-post-excerpt {
  font-size: 0.85rem;
  color: #666;
  margin: 0;
  line-height: 1.3;
}

/* Responsive */
@media (max-width: 1200px) {
  .main-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    position: static;
    max-height: none;
    margin-top: 2rem;
  }

  .categories-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
}

@media (max-width: 992px) {
  .content-wrapper {
    flex-direction: column;
  }

  .main-content {
    padding-right: 0;
  }

  .posts-grid {
    grid-template-columns: 1fr;
  }

  .sidebar {
    padding: 1rem;
  }

  .related-posts-grid {
    grid-template-columns: 1fr;
  }

  .categories-grid {
    grid-template-columns: 1fr;
  }

  .browse-categories {
    margin-top: 2rem;
    padding-top: 1.5rem;
  }
}



/* Navigation Enhancements */
.breadcrumb {
  background-color: var(--background-color);
  padding: 0.5rem 1rem;
  border-radius: var(--border-radius);
  margin-bottom: 1rem;
  font-size: 0.9rem;
}

.breadcrumb a {
  color: var(--link-color);
  border-bottom: none;
}

.breadcrumb a:hover {
  color: var(--link-hover-color);
  border-bottom: 1px solid var(--link-hover-color);
}

.breadcrumb-separator {
  margin: 0 0.5rem;
  color: #888;
}

.table-of-contents {
  background-color: var(--background-color);
  border: 1px solid #ddd;
  border-radius: var(--border-radius);
  padding: 1rem;
  margin: 1rem 0;
  max-width: 300px;
  float: right;
  margin-left: 1rem;
}

.table-of-contents h3 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
  color: var(--primary-color);
}

.table-of-contents ul {
  list-style: none;
  padding-left: 0;
  margin: 0;
}

.table-of-contents li {
  margin-bottom: 0.3rem;
}

.table-of-contents a {
  font-size: 0.9rem;
  color: var(--text-color);
  border-bottom: 1px dotted var(--link-color);
}

.table-of-contents a:hover {
  color: var(--link-hover-color);
  border-bottom: 1px solid var(--link-hover-color);
}

.post-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 2rem 0;
  padding: 1rem;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
}

.post-nav-link {
  display: flex;
  flex-direction: column;
  text-align: center;
  max-width: 45%;
}

.post-nav-label {
  font-size: 0.8rem;
  color: #888;
  margin-bottom: 0.3rem;
}

.post-nav-title {
  font-weight: 600;
  color: var(--link-color);
}

.back-to-top {
  position: fixed;
  bottom: 2rem;
  right: 2rem;
  background-color: var(--primary-color);
  color: white;
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  cursor: pointer;
  transition: var(--transition);
  opacity: 0;
  visibility: hidden;
  z-index: 100;
}

.back-to-top.visible {
  opacity: 1;
  visibility: visible;
}

.back-to-top:hover {
  background-color: var(--secondary-color);
  transform: translateY(-2px);
}

/* Enhanced Related Posts */
.related-posts-enhanced {
  margin: 2rem 0;
  padding: 1.5rem;
  background-color: var(--background-color);
  border-radius: var(--border-radius);
}

.related-posts-enhanced h3 {
  margin-top: 0;
  color: var(--primary-color);
  border-bottom: 2px solid var(--secondary-color);
  padding-bottom: 0.5rem;
}

.related-posts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.related-post-item {
  background-color: var(--card-background);
  padding: 1rem;
  border-radius: var(--border-radius);
  border: 1px solid #eee;
  transition: var(--transition);
}

.related-post-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow);
}

.related-post-item h4 {
  margin-top: 0;
  margin-bottom: 0.5rem;
  font-size: 1rem;
}

.related-post-item a {
  color: var(--text-color);
  background: linear-gradient(to bottom, transparent 60%, rgba(107, 91, 149, 0.2) 60%);
  border-bottom: 1px dotted var(--link-color);
  padding: 0 2px;
}

.related-post-excerpt {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.related-post-meta {
  font-size: 0.8rem;
  color: #888;
}

/* Post Header Styles */
.post-meta {
  color: #666;
  font-size: 0.9rem;
  margin-bottom: 1rem;
  padding-bottom: 0.5rem;
}

.post-author {
  font-weight: 600;
}

.post-date {
  color: #888;
}

.post-excerpt {
  background-color: #f8f9fa;
  border-left: 4px solid var(--secondary-color);
  padding: 1rem;
  margin: 1rem 0;
  font-style: italic;
  color: #555;
}

.post-tags {
  margin: 1rem 0;
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.post-tags .tag {
  background-color: var(--primary-color);
  color: white;
  padding: 0.3rem 0.8rem;
  border-radius: 20px;
  font-size: 0.8rem;
  font-weight: 500;
}

.post-separator {
  border: none;
  height: 2px;
  background: linear-gradient(to right, var(--primary-color), var(--secondary-color));
  margin: 2rem 0;
  border-radius: 1px;
}

/* Post list styling */
.post-list ul.posts {
  list-style: none;
  padding: 0;
  margin: 2rem 0;
}

.post-list .post-item {
  display: flex;
  margin-bottom: 2rem;
  padding-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.post-thumbnail {
  flex: 0 0 200px;
  margin-right: 1.5rem;
}

.post-thumbnail img {
  width: 100%;
  height: auto;
  object-fit: cover;
  border-radius: 4px;
}

.post-content-preview {
  flex: 1;
}

.post-title-link {
  font-size: 1.4rem;
  font-weight: bold;
  color: #333;
  text-decoration: none;
  display: block;
  margin-bottom: 0.5rem;
}

.post-title-link:hover {
  color: var(--primary-color);
}

.post-excerpt {
  color: #666;
  margin-bottom: 1rem;
}

.post-meta {
  font-size: 0.9rem;
  color: #888;
  display: flex;
  gap: 1rem;
}

.post-meta .post-date {
  color: #888;
}

.post-meta .post-author {
  font-weight: 600;
}

.post-tags {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

.post-tags .tag {
  background-color: var(--tag-background);
  color: var(--text-color);
  padding: 0.2rem 0.6rem;
  border-radius: 15px;
  font-size: 0.8rem;
  border: 1px solid #ddd;
}

/* Post Grid Layout */
.post-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 2rem;
  margin: 2rem 0;
}

/* Post Card Styling - Fixed version */
.post-card {
  display: block;
  background-color: #fff;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  text-decoration: none;
  color: #333;
  height: 100%;
  border: 1px solid #eee;
  position: relative; /* Add positioning context */
}

.post-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.15);
}

.post-card:hover .post-card-title {
  color: var(--primary-color);
}

/* Remove any pseudo-elements that might be causing the brackets */
.post-card:before,
.post-card:after {
  display: none !important;
}

.post-card-thumbnail {
  width: 100%;
  height: 180px;
  overflow: hidden;
  position: relative;
}

.post-card-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.post-card:hover .post-card-thumbnail img {
  transform: scale(1.05);
}

.post-card-thumbnail img.placeholder {
  opacity: 0.8;
}

.post-card-content {
  padding: 1.25rem;
  background-color: #fff; /* Explicitly set background */
  position: relative; /* Ensure content stays above any pseudo-elements */
  z-index: 1;
}

.post-card-title {
  font-size: 1.25rem;
  margin-top: 0;
  margin-bottom: 0.75rem;
  color: #333;
  transition: color 0.3s ease;
}

.post-card-excerpt {
  font-size: 0.95rem;
  margin-bottom: 1rem;
  color: #555;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.5;
}

.post-card-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 0.75rem;
  font-size: 0.85rem;
  color: #777;
  margin-bottom: 0.75rem;
}

.post-card-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-top: 0.5rem;
  position: relative; /* Ensure tags stay above any pseudo-elements */
  z-index: 2;
}

.post-card-tags .tag {
  font-size: 0.75rem;
  background-color: #f0f0f0;
  color: #555;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  display: inline-block;
}

/* Dark theme specific overrides are now handled by [data-theme="dark"] selectors above */

/* Category content styling */
.category-content {
  margin: 2rem 0;
}

.category-header h1 {
  color: var(--primary-color, #333);
  margin-bottom: 0.5rem;
}

.category-description {
  color: #666;
  font-size: 1.1rem;
  margin-bottom: 2rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .post-grid {
    grid-template-columns: 1fr;
  }
  
  .post-card-thumbnail {
    height: 200px;
  }
}

/* Old dark mode media query removed - now using data-theme attributes */

/* Make entire card clickable */
.post-card-link {
  display: block;
  text-decoration: none;
  color: inherit;
  height: 100%;
}

.post-card-link:hover {
  text-decoration: none;
  color: inherit;
  border-bottom: none;
}

/* Override any default link styling */
.post-card-link:hover,
.post-card-link:focus,
.post-card-link:active {
  text-decoration: none;
  border-bottom: none;
}

/* Graph View Styles */
#graph-container {
  background-color: var(--background-color);
  border-radius: var(--border-radius);
  overflow: hidden;
}

#graph-container svg {
  width: 100%;
  height: 100%;
}

#graph-container circle {
  cursor: pointer;
  transition: all 0.2s ease;
}

#graph-container circle:hover {
  filter: brightness(1.2);
}

#graph-container text {
  pointer-events: none;
  font-family: 'Open Sans', sans-serif;
  font-size: 10px;
  fill: var(--text-color);
  text-shadow: 0 1px 0 #fff, 1px 0 0 #fff, 0 -1px 0 #fff, -1px 0 0 #fff;
}

.graph-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background-color: var(--card-background);
  border-radius: var(--border-radius);
}

.legend-item {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 5px;
}

/* Comments System Styles */
.comments-section {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 2px solid var(--secondary-color);
}

.comments-header h3 {
    color: var(--primary-color);
    margin-bottom: 1.5rem;
    font-size: 1.5rem;
}

/* Comment Form */
.comment-form-container {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--card-background);
    border-radius: var(--border-radius);
    border: 1px solid #eee;
    box-shadow: var(--box-shadow);
}

.user-info {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    gap: 0.75rem;
}

.user-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    object-fit: cover;
}

.user-name {
    font-weight: 600;
    color: var(--text-color);
}

.logout-link {
    margin-left: auto;
    color: var(--link-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.logout-link:hover {
    text-decoration: underline;
}

.comment-form .form-group {
    margin-bottom: 1rem;
}

.comment-form textarea {
    width: 100%;
    min-height: 100px;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-family: inherit;
    font-size: 0.95rem;
    line-height: 1.5;
    resize: vertical;
    background: white;
    color: #333;
}

.comment-form textarea:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(107, 91, 149, 0.2);
}

.character-count {
    text-align: right;
    font-size: 0.8rem;
    color: #666;
    margin-top: 0.25rem;
}

.form-actions {
    display: flex;
    gap: 0.75rem;
    justify-content: flex-end;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background: #5a4a82;
}

.btn-secondary {
    background: #ddd;
    color: var(--text-color);
}

.btn-secondary:hover {
    background: #bbb;
}

.btn-google {
    background: #4285f4;
    color: white;
    padding: 0.75rem 1.5rem;
}

.btn-google:hover {
    background: #3367d6;
}

/* Login Prompt */
.login-prompt {
    text-align: center;
    padding: 2rem;
    background: var(--card-background);
    border-radius: var(--border-radius);
    border: 1px solid #eee;
    margin-bottom: 2rem;
    box-shadow: var(--box-shadow);
}

.login-prompt p {
    margin-bottom: 1rem;
    color: #666;
}

/* Comments List */
.comments-container {
    margin-top: 2rem;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.comment {
    padding: 1.5rem;
    border: 1px solid #eee;
    border-radius: var(--border-radius);
    background: var(--card-background);
    margin-bottom: 1rem;
    box-shadow: var(--box-shadow);
}

.comment-reply {
    margin-left: 2rem;
    margin-top: 1rem;
    border-left: 3px solid var(--secondary-color);
    padding-left: 1rem;
}

.comment-header {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    gap: 0.75rem;
}

.comment-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.comment-meta {
    display: flex;
    flex-direction: column;
    gap: 0.25rem;
}

.comment-author {
    font-weight: 600;
    color: #333;
}

.comment-date {
    font-size: 0.8rem;
    color: #666;
}

.comment-content {
    margin-bottom: 1rem;
    line-height: 1.6;
    color: #333;
}

.comment-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.vote-btn, .reply-btn {
    background: none;
    border: 1px solid #ddd;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    color: #666;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.vote-btn:hover, .reply-btn:hover {
    background: #f5f5f5;
    color: var(--text-color);
}

.vote-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.vote-display {
    font-size: 0.8rem;
    color: #666;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.vote-count {
    font-weight: 500;
}

/* Messages */
.message {
    padding: 0.75rem 1rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    font-size: 0.9rem;
}

.message-success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.message-error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Load More */
#load-more-container {
    text-align: center;
    margin-top: 2rem;
}

/* Screen Reader Only */
.sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Comments responsive styles */
@media (max-width: 768px) {
  .comment-reply {
    margin-left: 1rem;
  }

  .comment-actions {
    flex-wrap: wrap;
    gap: 0.5rem;
  }

  .form-actions {
    flex-direction: column;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }
}

/* Gallery Styles */
.gallery-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.gallery-intro {
  text-align: center;
  margin-bottom: 2rem;
  color: #333 !important;
  font-size: 1.1rem;
  background: transparent;
}

.gallery-filters {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.filter-btn {
  padding: 0.5rem 1rem;
  border: 2px solid var(--primary-color);
  background: transparent;
  color: var(--primary-color);
  border-radius: 25px;
  cursor: pointer;
  transition: var(--transition);
  font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
  background: var(--primary-color);
  color: white;
}

.gallery-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.gallery-item {
  position: relative;
  overflow: hidden;
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  transition: var(--transition);
  cursor: pointer;
}

.gallery-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.gallery-item-inner {
  position: relative;
  width: 100%;
  height: 250px;
  overflow: hidden;
}

.gallery-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
  margin: 0;
}

.gallery-item:hover img {
  transform: scale(1.05);
}

.gallery-overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: linear-gradient(transparent, rgba(0,0,0,0.8));
  color: white;
  padding: 1rem;
  transform: translateY(100%);
  transition: var(--transition);
}

.gallery-item:hover .gallery-overlay {
  transform: translateY(0);
}

.gallery-info h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  color: white;
}

.gallery-category {
  font-size: 0.8rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Gallery Modal */
.gallery-modal {
  display: none;
  position: fixed;
  z-index: 1000;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0,0,0,0.9);
  animation: fadeIn 0.3s ease;
}

.gallery-modal.show {
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-content {
  position: relative;
  max-width: 90%;
  max-height: 90%;
  margin: auto;
  display: flex;
  flex-direction: column;
  background: var(--card-background);
  border-radius: var(--border-radius);
  overflow: hidden;
}

.modal-close {
  position: absolute;
  top: 10px;
  right: 15px;
  color: var(--text-color);
  font-size: 2rem;
  font-weight: bold;
  cursor: pointer;
  z-index: 1001;
  transition: var(--transition);
  background: var(--card-background);
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid var(--border-color);
}

.modal-close:hover {
  color: var(--accent-color);
}

.modal-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  flex-shrink: 0;
}

#modal-image {
  max-width: 100%;
  max-height: 60vh;
  object-fit: contain;
  margin: 0;
}

.modal-info {
  background: var(--card-background);
  color: var(--text-color);
  padding: 1rem;
  text-align: center;
  border-bottom: 1px solid var(--border-color);
  flex-shrink: 0;
}

.modal-info h3 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.2rem;
}

#modal-category {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.modal-commentary {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
  font-size: 0.95rem;
  line-height: 1.6;
  color: var(--text-muted);
}

.modal-nav {
  display: flex;
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--card-background);
  border-top: 1px solid var(--border-color);
  flex-shrink: 0;
}

.modal-nav-btn {
  background: var(--accent-color);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  border-radius: var(--border-radius);
  transition: var(--transition);
  min-width: 100px;
}

.modal-nav-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.modal-nav-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Modal Comments Section */
.modal-comments-section {
  flex: 1;
  overflow-y: auto;
  background: var(--card-background);
  border-top: 1px solid var(--border-color);
  padding: 1rem;
  display: flex;
  flex-direction: column;
  min-height: 0; /* Important for flex scrolling */
}

.modal-comments-section .comments-header h4 {
  margin: 0 0 0.5rem 0;
  color: var(--text-color);
  font-size: 1.1rem;
}

.modal-comments-section .comments-hint {
  color: var(--text-muted);
  font-size: 0.8rem;
  margin-bottom: 1rem;
  display: block;
}

.modal-comments-section .comment-form {
  margin-bottom: 1.5rem;
}

.modal-comments-section .form-group {
  margin-bottom: 1rem;
}

.modal-comments-section .form-group label {
  display: block;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: var(--text-color);
}

.modal-comments-section .form-group input,
.modal-comments-section .form-group textarea {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  background: var(--card-background);
  color: var(--text-color);
  font-family: inherit;
}

.modal-comments-section .form-group textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-comments-section .character-count {
  text-align: right;
  font-size: 0.8rem;
  color: var(--text-muted);
  margin-top: 0.25rem;
}

.modal-comments-section .form-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.modal-comments-section .btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: var(--border-radius);
  cursor: pointer;
  font-size: 0.9rem;
  transition: var(--transition);
}

.modal-comments-section .btn-primary {
  background: var(--accent-color);
  color: white;
}

.modal-comments-section .btn-secondary {
  background: var(--border-color);
  color: var(--text-color);
}

.modal-comments-section .btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.modal-comments-section .comments-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.modal-comments-section .comment {
  border-bottom: 1px solid var(--border-color);
  padding: 0.75rem 0;
}

.modal-comments-section .comment:last-child {
  border-bottom: none;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* Gallery Responsive Design */
@media (max-width: 768px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 0.75rem;
  }

  .gallery-item-inner {
    height: 200px;
  }

  .gallery-filters {
    gap: 0.25rem;
  }

  .filter-btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.9rem;
  }

  .modal-nav-btn {
    padding: 0.75rem;
    font-size: 1.2rem;
  }

  #modal-prev {
    margin-left: -30px;
  }

  #modal-next {
    margin-right: -30px;
  }

  .modal-content {
    max-width: 98%;
    max-height: 98%;
  }

  .modal-image-container {
    max-height: 50vh;
  }

  #modal-image {
    max-height: 50vh;
  }

  .modal-comments-section {
    max-height: 40vh;
    padding: 0.75rem;
  }

  .modal-comments-section .user-fields {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }

  .modal-content {
    max-width: 98%;
    max-height: 98%;
  }

  .modal-image-container {
    max-height: 40vh;
  }

  #modal-image {
    max-height: 40vh;
  }
}

@media (max-width: 480px) {
  .gallery-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 0.5rem;
  }

  .gallery-item-inner {
    height: 150px;
  }

  .gallery-container {
    padding: 1rem;
  }
}

/* Donation Modal Styles */
.donation-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.donation-modal.show {
  opacity: 1;
  visibility: visible;
}

.donation-modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(5px);
}

.donation-modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--card-background);
  border-radius: 12px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  max-width: 500px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
  from {
    transform: translate(-50%, -60%);
    opacity: 0;
  }
  to {
    transform: translate(-50%, -50%);
    opacity: 1;
  }
}

.donation-modal-close {
  position: absolute;
  top: 15px;
  right: 15px;
  background: none;
  border: none;
  font-size: 24px;
  color: var(--text-muted);
  cursor: pointer;
  transition: var(--transition);
  z-index: 1;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

.donation-modal-close:hover {
  color: var(--accent-color);
  background: var(--hover-background);
}

.donation-modal-body {
  padding: 2rem;
  text-align: center;
}

.donation-modal-title {
  color: var(--primary-color);
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.donation-modal-message {
  color: var(--text-color);
  margin: 0 0 2rem 0;
  line-height: 1.6;
  font-size: 1rem;
}

.donation-modal-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
}

.donation-modal-kofi-btn {
  background: linear-gradient(135deg, #00b9fe, #0099d4);
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--transition);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 185, 254, 0.3);
}

.donation-modal-kofi-btn:hover {
  background: linear-gradient(135deg, #0099d4, #007bb3);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 185, 254, 0.4);
}

.kofi-icon {
  font-size: 1.2rem;
}

.donation-modal-widget-btn {
  background: var(--secondary-color);
  color: white;
  border: none;
  padding: 0.6rem 1.25rem;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.donation-modal-widget-btn:hover {
  background: #7a9c42;
  transform: translateY(-1px);
}

.donation-modal-later-btn {
  background: transparent;
  color: var(--text-muted);
  border: 1px solid var(--border-color);
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.85rem;
  cursor: pointer;
  transition: var(--transition);
}

.donation-modal-later-btn:hover {
  background: var(--hover-background);
  color: var(--text-color);
  border-color: var(--text-muted);
}

.donation-modal-footer {
  margin: 0;
  color: var(--text-light);
  font-size: 0.8rem;
  line-height: 1.4;
}

/* Light theme adjustments */
[data-theme="light"] .donation-modal-overlay {
  background: rgba(0, 0, 0, 0.4);
}

[data-theme="light"] .donation-modal-content {
  background: white;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
  border: 1px solid rgba(107, 91, 149, 0.1);
}

/* Dark theme adjustments */
[data-theme="dark"] .donation-modal-overlay {
  background: rgba(0, 0, 0, 0.8);
}

[data-theme="dark"] .donation-modal-content {
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.6);
}

/* Gradient theme adjustments */
[data-theme="gradient"] .donation-modal-content {
  background: linear-gradient(135deg, rgba(255,255,255,0.95), rgba(248,250,252,0.95));
  backdrop-filter: blur(15px);
  border: 1px solid rgba(139, 92, 246, 0.2);
}

[data-theme="gradient"] .donation-modal-kofi-btn {
  background: linear-gradient(135deg, #8b5cf6, #06d6a0);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

[data-theme="gradient"] .donation-modal-kofi-btn:hover {
  background: linear-gradient(135deg, #7c3aed, #059669);
  box-shadow: 0 6px 16px rgba(139, 92, 246, 0.4);
}

/* Responsive design */
@media (max-width: 768px) {
  .donation-modal-content {
    max-width: 95%;
    margin: 1rem;
  }

  .donation-modal-body {
    padding: 1.5rem;
  }

  .donation-modal-title {
    font-size: 1.3rem;
  }

  .donation-modal-message {
    font-size: 0.95rem;
  }
}

@media (max-width: 480px) {
  .donation-modal-body {
    padding: 1rem;
  }

  .donation-modal-title {
    font-size: 1.2rem;
  }

  .donation-modal-buttons {
    gap: 0.5rem;
  }

  .donation-modal-kofi-btn {
    padding: 0.6rem 1rem;
    font-size: 0.9rem;
  }
}


