<?php
/**
 * Comments System Configuration
 * For A. A. Chips' Obsidian-Quartz Blog
 * Loads secure configuration from outside public_html
 */

// Define access constant for secure config
define('OBQ_CONFIG_ACCESS', true);

// Try to load secure configuration from outside public_html, fallback to local config
$secureConfigPath = __DIR__ . '/../../secure_config/obq_comments.php';
$localConfigPath = __DIR__ . '/local_config.php';

if (file_exists($secureConfigPath)) {
    try {
        $secureConfig = require $secureConfigPath;
        // Secure config loaded successfully
    } catch (Exception $e) {
        throw new Exception('Error loading secure configuration: ' . $e->getMessage());
    }
} elseif (file_exists($localConfigPath)) {
    try {
        $secureConfig = require $localConfigPath;
        // Local config loaded successfully
    } catch (Exception $e) {
        throw new Exception('Error loading local configuration: ' . $e->getMessage());
    }
} else {
    // Default XAMPP configuration for local development
    $secureConfig = [
        'database' => [
            'host' => 'localhost',
            'dbname' => 'aachipsc',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4',
            'table_prefix' => 'aachipsc_blog_',
            'options' => [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
            ]
        ],
        'auth' => [
            'session_timeout' => 86400 * 7, // 7 days
            'require_email_verification' => false,
        ]
    ];
    // Using default XAMPP configuration
}

// Merge secure config with public settings
$config = array_merge($secureConfig, [

    // Comment System Settings
    'comments' => [
        'require_approval' => false, // Set to true for manual moderation
        'max_comment_length' => 2000,
        'min_comment_length' => 10,
        'allow_html' => false, // Allow basic HTML tags
        'allowed_html_tags' => '<p><br><strong><em><a><ul><ol><li>',
        'enable_threading' => true, // Allow replies to comments
        'max_thread_depth' => 3,
        'comments_per_page' => 20,
        'enable_voting' => true, // Like/dislike functionality
    ],

    // Spam Protection
    'spam' => [
        'enable_spam_detection' => true,
        'spam_threshold' => 0.7, // 0.0 to 1.0, higher = more strict
        'auto_reject_spam' => false, // If true, auto-reject; if false, mark for review
        'honeypot_field' => 'website', // Hidden field name for bot detection
        'enable_rate_limiting' => true,
    ],

    // Rate Limiting (per IP address)
    'rate_limits' => [
        'comments_per_hour' => 10,
        'comments_per_day' => 50,
        'votes_per_hour' => 100,
        'login_attempts_per_hour' => 20,
    ],

    // Admin Settings
    'admin' => [
        'admin_google_ids' => [
            // Add your Google ID here for admin privileges
            // 'your_google_id_here'
        ],
        'notification_email' => '<EMAIL>', // For new comment notifications
        'enable_email_notifications' => false,
    ],

    // Security
    'security' => [
        'csrf_token_name' => 'csrf_token',
        'session_name' => 'obsidian_quartz_comments',
        'cookie_lifetime' => 86400 * 30, // 30 days
        'secure_cookies' => false, // Set to true for HTTPS
        'same_site_cookies' => 'Lax',
    ],

    // Display Settings
    'display' => [
        'date_format' => 'F j, Y \a\t g:i A',
        'timezone' => 'America/New_York', // Change to your timezone
        'avatar_size' => 40,
        'show_user_email' => false, // Never show user emails publicly
        'show_comment_count' => true,
        'default_sort' => 'newest', // 'newest', 'oldest', 'most_liked'
    ],

    // Cache Settings (optional)
    'cache' => [
        'enable_cache' => false,
        'cache_duration' => 300, // 5 minutes
        'cache_directory' => __DIR__ . '/cache/',
    ]
]);

return $config;

