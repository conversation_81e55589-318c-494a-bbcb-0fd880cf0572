-- <PERSON><PERSON><PERSON><PERSON> Chips Blog Database Export for Production
-- Generated on: 2025-07-24 01:29:29
-- Source: Local XAMPP Database
-- Target: Production Webhost
-- Tables exported in dependency order to avoid foreign key conflicts

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
SET AUTOCOMMIT = 0;
START TRANSACTION;
SET time_zone = "+00:00";

-- Disable foreign key checks during import
SET FOREIGN_KEY_CHECKS = 0;

-- Table structure for table `aachipsc_blog_users`
DROP TABLE IF EXISTS `aachipsc_blog_users`;
CREATE TABLE `aachipsc_blog_users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `email` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `is_banned` tinyint(1) DEFAULT 0,
  PRIMARY KEY (`id`),
  <PERSON><PERSON><PERSON><PERSON> KEY `unique_email_name` (`email`,`name`),
  <PERSON><PERSON><PERSON> `idx_email` (`email`),
  KEY `idx_name` (`name`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `aachipsc_blog_users`
INSERT INTO `aachipsc_blog_users` (`id`, `email`, `name`, `created_at`, `updated_at`, `is_banned`) VALUES
('1', '<EMAIL>', 'aachips', '2025-07-09 18:44:04', '2025-07-09 18:44:04', '0'),
('2', '<EMAIL>', 'Debug User', '2025-07-09 18:53:03', '2025-07-09 18:53:03', '0'),
('3', '<EMAIL>', 'Test User', '2025-07-09 18:53:09', '2025-07-09 18:53:09', '0'),
('4', '<EMAIL>', 'AJAX Test User', '2025-07-23 18:42:27', '2025-07-23 18:42:27', '0'),
('5', '<EMAIL>', 'Direct Test User', '2025-07-23 18:44:33', '2025-07-23 18:44:33', '0'),
('6', '<EMAIL>', 'Final Test User', '2025-07-23 18:46:19', '2025-07-23 18:46:19', '0');

-- Table structure for table `aachipsc_blog_spam_patterns`
DROP TABLE IF EXISTS `aachipsc_blog_spam_patterns`;
CREATE TABLE `aachipsc_blog_spam_patterns` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `pattern` varchar(500) NOT NULL,
  `pattern_type` enum('keyword','regex','url') NOT NULL,
  `weight` decimal(3,2) DEFAULT 0.50,
  `is_active` tinyint(1) DEFAULT 1,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_pattern_type` (`pattern_type`),
  KEY `idx_active` (`is_active`)
) ENGINE=InnoDB AUTO_INCREMENT=33 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `aachipsc_blog_spam_patterns`
INSERT INTO `aachipsc_blog_spam_patterns` (`id`, `pattern`, `pattern_type`, `weight`, `is_active`, `created_at`) VALUES
('1', 'viagra', 'keyword', '0.80', '1', '2025-07-09 18:14:20'),
('2', 'cialis', 'keyword', '0.80', '1', '2025-07-09 18:14:20'),
('3', 'casino', 'keyword', '0.60', '1', '2025-07-09 18:14:20'),
('4', 'lottery', 'keyword', '0.60', '1', '2025-07-09 18:14:20'),
('5', 'winner', 'keyword', '0.40', '1', '2025-07-09 18:14:20'),
('6', 'congratulations', 'keyword', '0.30', '1', '2025-07-09 18:14:20'),
('7', 'http[s]?://[^s]+.(tk|ml|ga|cf)', 'regex', '0.90', '1', '2025-07-09 18:14:20'),
('8', '[0-9]{10,}', 'regex', '0.50', '1', '2025-07-09 18:14:20'),
('9', 'viagra', 'keyword', '0.80', '1', '2025-07-09 18:14:50'),
('10', 'cialis', 'keyword', '0.80', '1', '2025-07-09 18:14:50'),
('11', 'casino', 'keyword', '0.60', '1', '2025-07-09 18:14:50'),
('12', 'lottery', 'keyword', '0.60', '1', '2025-07-09 18:14:50'),
('13', 'winner', 'keyword', '0.40', '1', '2025-07-09 18:14:50'),
('14', 'congratulations', 'keyword', '0.30', '1', '2025-07-09 18:14:50'),
('15', 'http[s]?://[^s]+.(tk|ml|ga|cf)', 'regex', '0.90', '1', '2025-07-09 18:14:50'),
('16', '[0-9]{10,}', 'regex', '0.50', '1', '2025-07-09 18:14:50'),
('17', 'viagra', 'keyword', '0.80', '1', '2025-07-09 18:15:32'),
('18', 'cialis', 'keyword', '0.80', '1', '2025-07-09 18:15:32'),
('19', 'casino', 'keyword', '0.60', '1', '2025-07-09 18:15:32'),
('20', 'lottery', 'keyword', '0.60', '1', '2025-07-09 18:15:32'),
('21', 'winner', 'keyword', '0.40', '1', '2025-07-09 18:15:32'),
('22', 'congratulations', 'keyword', '0.30', '1', '2025-07-09 18:15:32'),
('23', 'http[s]?://[^s]+.(tk|ml|ga|cf)', 'regex', '0.90', '1', '2025-07-09 18:15:32'),
('24', '[0-9]{10,}', 'regex', '0.50', '1', '2025-07-09 18:15:32'),
('25', 'viagra', 'keyword', '0.80', '1', '2025-07-09 18:43:11'),
('26', 'cialis', 'keyword', '0.80', '1', '2025-07-09 18:43:11'),
('27', 'casino', 'keyword', '0.60', '1', '2025-07-09 18:43:11'),
('28', 'lottery', 'keyword', '0.60', '1', '2025-07-09 18:43:11'),
('29', 'winner', 'keyword', '0.40', '1', '2025-07-09 18:43:11'),
('30', 'congratulations', 'keyword', '0.30', '1', '2025-07-09 18:43:11'),
('31', 'http[s]?://[^s]+.(tk|ml|ga|cf)', 'regex', '0.90', '1', '2025-07-09 18:43:11'),
('32', '[0-9]{10,}', 'regex', '0.50', '1', '2025-07-09 18:43:11');

-- Table structure for table `aachipsc_blog_site_stats`
DROP TABLE IF EXISTS `aachipsc_blog_site_stats`;
CREATE TABLE `aachipsc_blog_site_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stat_name` varchar(100) NOT NULL,
  `stat_value` bigint(20) DEFAULT 0,
  `stat_date` date DEFAULT NULL,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `stat_name` (`stat_name`),
  KEY `idx_stat_name` (`stat_name`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `aachipsc_blog_site_stats`
INSERT INTO `aachipsc_blog_site_stats` (`id`, `stat_name`, `stat_value`, `stat_date`, `last_updated`) VALUES
('1', 'total_site_visits', '21', NULL, '2025-07-23 18:52:29'),
('2', 'unique_site_visitors', '0', NULL, '2025-07-09 18:14:50'),
('3', 'total_pages_visited', '14', NULL, '2025-07-23 18:45:46'),
('4', 'site_launch_date', '1752099290', NULL, '2025-07-09 18:14:50');

-- Table structure for table `aachipsc_blog_daily_stats`
DROP TABLE IF EXISTS `aachipsc_blog_daily_stats`;
CREATE TABLE `aachipsc_blog_daily_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `stat_date` date NOT NULL,
  `total_visits` int(11) DEFAULT 0,
  `unique_visitors` int(11) DEFAULT 0,
  `pages_visited` int(11) DEFAULT 0,
  `top_page` varchar(255) DEFAULT NULL,
  `top_page_visits` int(11) DEFAULT 0,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_date` (`stat_date`),
  KEY `idx_stat_date` (`stat_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- No data to dump for table `aachipsc_blog_daily_stats`

-- Table structure for table `aachipsc_blog_page_stats`
DROP TABLE IF EXISTS `aachipsc_blog_page_stats`;
CREATE TABLE `aachipsc_blog_page_stats` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_slug` varchar(255) NOT NULL,
  `page_title` varchar(500) DEFAULT NULL,
  `total_visits` int(11) DEFAULT 0,
  `unique_visits` int(11) DEFAULT 0,
  `today_visits` int(11) DEFAULT 0,
  `today_unique_visits` int(11) DEFAULT 0,
  `last_visit` timestamp NULL DEFAULT NULL,
  `first_visit` timestamp NULL DEFAULT NULL,
  `last_updated` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `page_slug` (`page_slug`),
  KEY `idx_page_slug` (`page_slug`),
  KEY `idx_total_visits` (`total_visits`),
  KEY `idx_unique_visits` (`unique_visits`),
  KEY `idx_last_visit` (`last_visit`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `aachipsc_blog_page_stats`
INSERT INTO `aachipsc_blog_page_stats` (`id`, `page_slug`, `page_title`, `total_visits`, `unique_visits`, `today_visits`, `today_unique_visits`, `last_visit`, `first_visit`, `last_updated`) VALUES
('2', 'demo-page', 'Database Demo Page', '1', '1', '0', '0', '2025-07-09 18:18:31', '2025-07-09 18:18:31', '2025-07-09 18:18:32'),
('3', 'home', 'Welcome to my Digital Garden - A. A. Chips', '4', '2', '0', '0', '2025-07-23 18:37:52', '2025-07-09 18:21:02', '2025-07-23 18:37:52'),
('4', '30-things-about-myself', '30 Things I Know About Myself', '1', '1', '0', '0', '2025-07-09 18:23:37', '2025-07-09 18:23:37', '2025-07-09 18:23:37'),
('5', 'hanukkahs-light', 'Hanukkah\'s Light - A Story of Resistance - Then and Now', '1', '1', '0', '0', '2025-07-09 18:29:33', '2025-07-09 18:29:33', '2025-07-09 18:29:33'),
('7', 'journal-index', 'Journaling Index', '1', '1', '0', '0', '2025-07-09 18:33:36', '2025-07-09 18:33:36', '2025-07-09 18:33:36'),
('8', 'quiet-after', 'The Quiet After - June 4th, 2025', '1', '1', '0', '0', '2025-07-09 18:33:37', '2025-07-09 18:33:37', '2025-07-09 18:33:37'),
('9', 'gallery', 'Gallery', '3', '1', '0', '0', '2025-07-23 18:52:29', '2025-07-09 19:07:23', '2025-07-23 18:52:29'),
('10', 'test-image-comments', 'Test Image Comments', '1', '1', '0', '0', '2025-07-09 19:09:05', '2025-07-09 19:09:05', '2025-07-09 19:09:05'),
('11', 'debug-image-comments', 'Debug Image Comments', '1', '1', '0', '0', '2025-07-09 19:20:16', '2025-07-09 19:20:16', '2025-07-09 19:20:16'),
('12', 'inspiration-index', 'Inspiration', '1', '1', '0', '0', '2025-07-09 20:26:57', '2025-07-09 20:26:57', '2025-07-09 20:26:57'),
('14', 'music-player', 'Music Playlists', '2', '2', '2', '2', '2025-07-11 07:04:07', '2025-07-10 22:06:24', '2025-07-11 07:04:07'),
('17', 'reading-after-tbi', 'My Reading Journey after Traumatic Brain Injury', '1', '1', '1', '1', '2025-07-11 07:20:26', '2025-07-11 07:20:26', '2025-07-11 07:20:26'),
('19', 'chip-off-old-block', 'Chip Off the Old Block', '1', '1', '0', '0', '2025-07-23 18:37:59', '2025-07-23 18:37:59', '2025-07-23 18:37:59'),
('20', 'liturgy-of-the-drowned', 'The First Liturgy of the Drowned', '1', '1', '0', '0', '2025-07-23 18:45:46', '2025-07-23 18:45:46', '2025-07-23 18:45:46');

-- Table structure for table `aachipsc_blog_visitor_sessions`
DROP TABLE IF EXISTS `aachipsc_blog_visitor_sessions`;
CREATE TABLE `aachipsc_blog_visitor_sessions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `user_agent_hash` varchar(64) NOT NULL,
  `session_id` varchar(128) DEFAULT NULL,
  `first_visit` timestamp NOT NULL DEFAULT current_timestamp(),
  `last_visit` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `total_page_views` int(11) DEFAULT 1,
  `pages_visited` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_visitor` (`ip_address`,`user_agent_hash`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_user_agent_hash` (`user_agent_hash`),
  KEY `idx_session_id` (`session_id`),
  KEY `idx_first_visit` (`first_visit`),
  KEY `idx_last_visit` (`last_visit`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- No data to dump for table `aachipsc_blog_visitor_sessions`

-- Table structure for table `aachipsc_blog_page_visits`
DROP TABLE IF EXISTS `aachipsc_blog_page_visits`;
CREATE TABLE `aachipsc_blog_page_visits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `page_slug` varchar(255) NOT NULL,
  `page_title` varchar(500) DEFAULT NULL,
  `ip_address` varchar(45) NOT NULL,
  `user_agent_hash` varchar(64) NOT NULL,
  `session_id` varchar(128) DEFAULT NULL,
  `visit_date` date NOT NULL,
  `visit_timestamp` timestamp NOT NULL DEFAULT current_timestamp(),
  `is_unique_daily` tinyint(1) DEFAULT 1,
  `is_unique_total` tinyint(1) DEFAULT 1,
  `referrer` varchar(500) DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_daily_visit` (`page_slug`,`ip_address`,`user_agent_hash`,`visit_date`),
  KEY `idx_page_slug` (`page_slug`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_visit_date` (`visit_date`),
  KEY `idx_visit_timestamp` (`visit_timestamp`),
  KEY `idx_unique_daily` (`page_slug`,`visit_date`,`is_unique_daily`),
  KEY `idx_unique_total` (`page_slug`,`is_unique_total`)
) ENGINE=InnoDB AUTO_INCREMENT=22 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `aachipsc_blog_page_visits`
INSERT INTO `aachipsc_blog_page_visits` (`id`, `page_slug`, `page_title`, `ip_address`, `user_agent_hash`, `session_id`, `visit_date`, `visit_timestamp`, `is_unique_daily`, `is_unique_total`, `referrer`) VALUES
('2', 'demo-page', 'Database Demo Page', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', NULL, '2025-07-10', '2025-07-09 18:18:31', '1', '1', NULL),
('3', 'home', 'Welcome to my Digital Garden - A. A. Chips', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', NULL, '2025-07-10', '2025-07-09 18:21:02', '1', '1', NULL),
('4', '30-things-about-myself', '30 Things I Know About Myself', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-10', '2025-07-09 18:23:37', '1', '1', NULL),
('5', 'hanukkahs-light', 'Hanukkah\'s Light - A Story of Resistance - Then and Now', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-10', '2025-07-09 18:29:33', '1', '1', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz/content/30-things-about-myself.php'),
('6', 'home', 'Welcome to my Digital Garden - A. A. Chips', '0.0.0.0', 'e3b0c44298fc1c149afbf4c8996fb92427ae41e4649b934ca495991b7852b855', NULL, '2025-07-10', '2025-07-09 18:32:11', '1', '1', NULL),
('7', 'journal-index', 'Journaling Index', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'j9quibspbcet68ed880f9q3vf2', '2025-07-10', '2025-07-09 18:33:36', '1', '1', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz/content/30-things-about-myself.php'),
('8', 'quiet-after', 'The Quiet After - June 4th, 2025', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'j9quibspbcet68ed880f9q3vf2', '2025-07-10', '2025-07-09 18:33:37', '1', '1', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz/content/journal/index.php'),
('9', 'gallery', 'Gallery', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-10', '2025-07-09 19:07:23', '1', '1', NULL),
('10', 'test-image-comments', 'Test Image Comments', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-10', '2025-07-09 19:09:05', '1', '1', NULL),
('11', 'debug-image-comments', 'Debug Image Comments', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-10', '2025-07-09 19:20:16', '1', '1', NULL),
('12', 'inspiration-index', 'Inspiration', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-10', '2025-07-09 20:26:57', '1', '1', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz/content/30-things-about-myself.php'),
('13', 'gallery', 'Gallery', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-11', '2025-07-10 22:05:58', '1', '0', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz/gallery.php'),
('14', 'music-player', 'Music Playlists', '::1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-11', '2025-07-10 22:06:24', '1', '1', NULL),
('15', 'music-player', 'Music Playlists', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-11', '2025-07-11 07:04:07', '1', '1', NULL),
('16', 'home', 'Welcome to my Digital Garden - A. A. Chips', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-11', '2025-07-11 07:20:13', '1', '0', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz//content/playlists/music-player.php'),
('17', 'reading-after-tbi', 'My Reading Journey after Traumatic Brain Injury', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-11', '2025-07-11 07:20:26', '1', '1', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz//content/index.php'),
('18', 'home', 'Welcome to my Digital Garden - A. A. Chips', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-24', '2025-07-23 18:37:52', '1', '0', NULL),
('19', 'chip-off-old-block', 'Chip Off the Old Block', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-24', '2025-07-23 18:37:59', '1', '1', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz/content/index.php'),
('20', 'liturgy-of-the-drowned', 'The First Liturgy of the Drowned', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-24', '2025-07-23 18:45:46', '1', '1', NULL),
('21', 'gallery', 'Gallery', '127.0.0.1', 'ee7a60193464f3a928720689228b16ca9a6f04c03806beafcf11f9a743712746', 'n41pfida2suc83v4jsocahaob4', '2025-07-24', '2025-07-23 18:52:29', '1', '0', 'http://localhost/webtech/coursework/Aprils%20Apple%20Chips%20HTML/aachips/public/obsidian-quartz/content/judaism/liturgy-of-the-drowned.php');

-- Table structure for table `aachipsc_blog_rate_limits`
DROP TABLE IF EXISTS `aachipsc_blog_rate_limits`;
CREATE TABLE `aachipsc_blog_rate_limits` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `ip_address` varchar(45) NOT NULL,
  `user_id` int(11) DEFAULT NULL,
  `action_type` varchar(50) NOT NULL,
  `window_start` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  KEY `idx_ip_address` (`ip_address`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_action_type` (`action_type`),
  KEY `idx_window_start` (`window_start`),
  CONSTRAINT `aachipsc_blog_rate_limits_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `aachipsc_blog_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=28 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `aachipsc_blog_rate_limits`
INSERT INTO `aachipsc_blog_rate_limits` (`id`, `ip_address`, `user_id`, `action_type`, `window_start`) VALUES
('1', '127.0.0.1', '1', 'comment', '2025-07-09 18:44:04'),
('2', '127.0.0.1', '1', 'comment', '2025-07-09 18:46:59'),
('3', '127.0.0.1', '1', 'comment', '2025-07-09 18:52:03'),
('4', '127.0.0.1', '2', 'comment', '2025-07-09 18:53:03'),
('5', '127.0.0.1', '3', 'comment', '2025-07-09 18:53:09'),
('6', '127.0.0.1', '1', 'comment', '2025-07-09 18:57:43'),
('7', '127.0.0.1', '1', 'vote', '2025-07-09 19:00:19'),
('8', '127.0.0.1', '1', 'vote', '2025-07-09 19:00:21'),
('9', '127.0.0.1', '3', 'comment', '2025-07-11 07:20:42'),
('10', '127.0.0.1', '3', 'comment', '2025-07-11 07:20:49'),
('11', '127.0.0.1', '1', 'comment', '2025-07-11 22:30:34'),
('12', '127.0.0.1', '1', 'comment', '2025-07-11 22:30:34'),
('13', '127.0.0.1', '1', 'comment', '2025-07-23 18:38:09'),
('14', '127.0.0.1', '1', 'comment', '2025-07-23 18:38:21'),
('15', '::1', '4', 'comment', '2025-07-23 18:42:27'),
('16', '::1', '5', 'comment', '2025-07-23 18:44:33'),
('17', '::1', '5', 'comment', '2025-07-23 18:45:24'),
('18', '127.0.0.1', '3', 'comment', '2025-07-23 18:45:40'),
('19', '127.0.0.1', '3', 'comment', '2025-07-23 18:46:09'),
('20', '127.0.0.1', '3', 'vote', '2025-07-23 18:46:15'),
('21', '127.0.0.1', '3', 'vote', '2025-07-23 18:46:16'),
('22', '127.0.0.1', '3', 'vote', '2025-07-23 18:46:17'),
('23', '127.0.0.1', '6', 'comment', '2025-07-23 18:46:19'),
('24', '127.0.0.1', '1', 'comment', '2025-07-23 18:54:18'),
('25', '127.0.0.1', '1', 'comment', '2025-07-23 18:54:18'),
('26', '127.0.0.1', '1', 'comment', '2025-07-23 18:54:36'),
('27', '127.0.0.1', '1', 'comment', '2025-07-23 18:54:36');

-- Table structure for table `aachipsc_blog_comments`
DROP TABLE IF EXISTS `aachipsc_blog_comments`;
CREATE TABLE `aachipsc_blog_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `post_slug` varchar(255) NOT NULL,
  `user_id` int(11) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `content` text NOT NULL,
  `is_approved` tinyint(1) DEFAULT 1,
  `is_spam` tinyint(1) DEFAULT 0,
  `spam_score` decimal(3,2) DEFAULT 0.00,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  PRIMARY KEY (`id`),
  KEY `idx_post_slug` (`post_slug`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_approved_spam` (`is_approved`,`is_spam`),
  CONSTRAINT `aachipsc_blog_comments_ibfk_1` FOREIGN KEY (`user_id`) REFERENCES `aachipsc_blog_users` (`id`) ON DELETE CASCADE,
  CONSTRAINT `aachipsc_blog_comments_ibfk_2` FOREIGN KEY (`parent_id`) REFERENCES `aachipsc_blog_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=24 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `aachipsc_blog_comments`
INSERT INTO `aachipsc_blog_comments` (`id`, `post_slug`, `user_id`, `parent_id`, `content`, `is_approved`, `is_spam`, `spam_score`, `created_at`, `updated_at`, `ip_address`, `user_agent`) VALUES
('1', 'quiet-after', '1', NULL, 'Test Comment 1, 2, 3', '1', '0', '0.00', '2025-07-09 18:44:04', '2025-07-09 18:44:04', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('2', 'quiet-after', '1', NULL, 'This is a test comment. 1, 2, 3.', '1', '0', '0.00', '2025-07-09 18:46:59', '2025-07-09 18:46:59', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('3', 'quiet-after', '1', NULL, 'This is a test comment. 1, 2, 3.', '1', '0', '0.00', '2025-07-09 18:52:03', '2025-07-09 18:52:03', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('4', 'debug-test', '2', NULL, 'This is a debug test comment', '1', '0', '0.00', '2025-07-09 18:53:03', '2025-07-09 18:53:03', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('5', 'manual-test', '3', NULL, 'This is a test comment to debug the submission process.', '1', '0', '0.00', '2025-07-09 18:53:09', '2025-07-09 18:53:09', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('6', 'quiet-after', '1', NULL, 'Test comment 1,2,3', '1', '0', '0.00', '2025-07-09 18:57:43', '2025-07-09 18:57:43', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('7', 'reading-after-tbi', '3', NULL, 'Test comment 1, 2, 3', '1', '0', '0.00', '2025-07-11 07:20:42', '2025-07-11 07:20:42', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('8', 'reading-after-tbi', '3', NULL, 'Test comment 1, 2, 3', '1', '0', '0.00', '2025-07-11 07:20:49', '2025-07-11 07:20:49', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('9', 'image-humor-mars-png', '1', NULL, 'Test Comment 1, 2, 3', '1', '0', '0.00', '2025-07-11 22:30:34', '2025-07-11 22:30:34', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('10', 'image-humor-mars-png', '1', NULL, 'Test Comment 1, 2, 3', '1', '0', '0.00', '2025-07-11 22:30:34', '2025-07-11 22:30:34', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('11', 'chip-off-old-block', '1', NULL, 'Test comment 1,2, 3.', '1', '0', '0.00', '2025-07-23 18:38:09', '2025-07-23 18:38:09', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('12', 'chip-off-old-block', '1', NULL, 'Test comment 1,2, 3.', '1', '0', '0.00', '2025-07-23 18:38:21', '2025-07-23 18:38:21', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('13', 'test-ajax', '4', NULL, 'This is a test comment via AJAX simulation', '1', '0', '0.00', '2025-07-23 18:42:27', '2025-07-23 18:42:27', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
('14', 'minimal-test-post', '3', NULL, 'This is a minimal test comment', '1', '0', '0.00', '2025-07-23 18:43:42', '2025-07-23 18:43:42', '127.0.0.1', 'Test User Agent'),
('15', 'direct-handler-test', '5', NULL, 'This is a direct handler test comment', '1', '0', '0.00', '2025-07-23 18:44:33', '2025-07-23 18:44:33', '::1', ''),
('16', 'direct-handler-test', '5', NULL, 'This is a direct handler test comment', '1', '0', '0.00', '2025-07-23 18:45:24', '2025-07-23 18:45:24', '::1', ''),
('17', 'manual-test', '3', NULL, 'This is a test comment to debug the submission process.', '1', '0', '0.00', '2025-07-23 18:45:40', '2025-07-23 18:45:40', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('18', 'liturgy-of-the-drowned', '3', NULL, 'Test comment 1, 2, 3', '1', '0', '0.00', '2025-07-23 18:46:09', '2025-07-23 18:46:09', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('19', 'final-test', '6', NULL, 'This is a final test comment to verify the fix.', '1', '0', '0.00', '2025-07-23 18:46:19', '2025-07-23 18:46:19', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('20', 'image-self-crochet1-jpg', '1', NULL, 'Test comment, 1, 2, 3', '1', '0', '0.00', '2025-07-23 18:54:18', '2025-07-23 18:54:18', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('21', 'image-self-crochet1-jpg', '1', NULL, 'Test comment, 1, 2, 3', '1', '0', '0.00', '2025-07-23 18:54:18', '2025-07-23 18:54:18', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('22', 'image-self-crochet1-jpg', '1', NULL, 'test comment 1 2 3', '1', '0', '0.00', '2025-07-23 18:54:36', '2025-07-23 18:54:36', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0'),
('23', 'image-self-crochet1-jpg', '1', NULL, 'test comment 1 2 3', '1', '0', '0.00', '2025-07-23 18:54:36', '2025-07-23 18:54:36', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0');

-- Table structure for table `aachipsc_blog_comment_votes`
DROP TABLE IF EXISTS `aachipsc_blog_comment_votes`;
CREATE TABLE `aachipsc_blog_comment_votes` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `comment_id` int(11) NOT NULL,
  `user_id` int(11) NOT NULL,
  `vote_type` enum('like','dislike') NOT NULL,
  `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_vote` (`comment_id`,`user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_user_id` (`user_id`),
  CONSTRAINT `aachipsc_blog_comment_votes_ibfk_1` FOREIGN KEY (`comment_id`) REFERENCES `aachipsc_blog_comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `aachipsc_blog_comment_votes_ibfk_2` FOREIGN KEY (`user_id`) REFERENCES `aachipsc_blog_users` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB AUTO_INCREMENT=6 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Dumping data for table `aachipsc_blog_comment_votes`
INSERT INTO `aachipsc_blog_comment_votes` (`id`, `comment_id`, `user_id`, `vote_type`, `created_at`) VALUES
('1', '3', '1', 'like', '2025-07-09 19:00:19'),
('2', '6', '1', 'like', '2025-07-09 19:00:21'),
('3', '18', '3', 'like', '2025-07-23 18:46:15');


-- Re-enable foreign key checks
SET FOREIGN_KEY_CHECKS = 1;
COMMIT;
